#-----------------------------------------------------------------------------
#
#  Copyright (C) 2006-2017  <PERSON><PERSON><PERSON>, Ingenieurgemeinschaft IgH
#
#  This file is part of the IgH EtherCAT Master.
#
#  The IgH EtherCAT Master is free software; you can redistribute it and/or
#  modify it under the terms of the GNU General Public License version 2, as
#  published by the Free Software Foundation.
#
#  The IgH EtherCAT Master is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
#  Public License for more details.
#
#  You should have received a copy of the GNU General Public License along
#  with the IgH EtherCAT Master; if not, write to the Free Software
#  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
#
#  ---
#
#  vim: syntax=make
#
#-----------------------------------------------------------------------------

src := @abs_srcdir@
ccflags-y := -I@abs_top_builddir@

TOPDIR := $(src)/../..

REV := $(shell if test -s $(TOPDIR)/revision; then \
		cat $(TOPDIR)/revision; \
	else \
		git -C $(TOPDIR) describe 2>/dev/null || echo "unknown"; \
	fi)

ifeq (@ENABLE_IGB@,1)
	obj-m += ec_igb.o

ifeq (@ENABLE_DRIVER_RESOURCE_VERIFYING@,1)
	ccflags-y := -DEC_ENABLE_DRIVER_RESOURCE_VERIFYING
endif

	ec_igb-objs := \
		e1000_82575-@KERNEL_IGB@-ethercat.o \
		e1000_i210-@KERNEL_IGB@-ethercat.o \
		e1000_mac-@KERNEL_IGB@-ethercat.o \
		e1000_mbx-@KERNEL_IGB@-ethercat.o \
		e1000_nvm-@KERNEL_IGB@-ethercat.o \
		e1000_phy-@KERNEL_IGB@-ethercat.o \
		igb_ethtool-@KERNEL_IGB@-ethercat.o \
		igb_hwmon-@KERNEL_IGB@-ethercat.o \
		igb_main-@KERNEL_IGB@-ethercat.o \
		igb_ptp-@KERNEL_IGB@-ethercat.o

	CFLAGS_igb_main-@KERNEL_IGB@-ethercat.o = -DREV=$(REV)
endif

KBUILD_EXTRA_SYMBOLS := \
	@abs_top_builddir@/$(LINUX_SYMVERS) \
	@abs_top_builddir@/master/$(LINUX_SYMVERS)

#-----------------------------------------------------------------------------
