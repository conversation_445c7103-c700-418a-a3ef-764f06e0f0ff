/**
    Network Driver for Beckhoff CCAT communication controller
    Copyright (C) 2014  Beckhoff Automation GmbH
    Author: <PERSON> <<EMAIL>>

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License along
    with this program; if not, write to the Free Software Foundation, Inc.,
    51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
*/

#ifndef _UPDATE_H_
#define _UPDATE_H_
extern u8 ccat_get_prom_id(void __iomem * const ioaddr);
extern struct ccat_update *ccat_update_init(const struct ccat_device *ccatdev,
					    void __iomem * addr);
extern void ccat_update_remove(struct ccat_update *update);
#endif /* #ifndef _UPDATE_H_ */
