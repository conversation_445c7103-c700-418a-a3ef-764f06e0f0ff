#-----------------------------------------------------------------------------
#
#  Copyright (C) 2006-2008  <PERSON><PERSON><PERSON>, Ingenieurgemeinschaft IgH
#
#  This file is part of the IgH EtherCAT Master.
#
#  The IgH EtherCAT Master is free software; you can redistribute it and/or
#  modify it under the terms of the GNU General Public License version 2, as
#  published by the Free Software Foundation.
#
#  The IgH EtherCAT Master is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
#  Public License for more details.
#
#  You should have received a copy of the GNU General Public License along
#  with the IgH EtherCAT Master; if not, write to the Free Software
#  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
#
#-----------------------------------------------------------------------------

include $(top_srcdir)/Makefile.kbuild

EXTRA_DIST = \
	LICENSE \
	e1000-3.0-ethercat.h \
	e1000-3.0-orig.h \
	e1000-3.10-ethercat.h \
	e1000-3.10-orig.h \
	e1000-3.12-ethercat.h \
	e1000-3.12-orig.h \
	e1000-3.14-ethercat.h \
	e1000-3.14-orig.h \
	e1000-3.16-ethercat.h \
	e1000-3.16-orig.h \
	e1000-3.4-ethercat.h \
	e1000-3.4-orig.h \
	e1000-3.6-ethercat.h \
	e1000-3.6-orig.h \
	e1000-3.8-ethercat.h \
	e1000-3.8-orig.h \
	e1000-4.4-ethercat.h \
	e1000-4.4-orig.h \
	e1000-5.10-ethercat.h \
	e1000-5.10-orig.h \
	e1000-5.14-ethercat.h \
	e1000-5.14-orig.h \
	e1000-5.15-ethercat.h \
	e1000-5.15-orig.h \
	e1000-6.1-ethercat.h \
	e1000-6.1-orig.h \
	e1000-6.12-ethercat.h \
	e1000-6.12-orig.h \
	e1000-6.4-ethercat.h \
	e1000-6.4-orig.h \
	e1000_ethtool-3.0-ethercat.c \
	e1000_ethtool-3.0-orig.c \
	e1000_ethtool-3.10-ethercat.c \
	e1000_ethtool-3.10-orig.c \
	e1000_ethtool-3.12-ethercat.c \
	e1000_ethtool-3.12-orig.c \
	e1000_ethtool-3.14-ethercat.c \
	e1000_ethtool-3.14-orig.c \
	e1000_ethtool-3.16-ethercat.c \
	e1000_ethtool-3.16-orig.c \
	e1000_ethtool-3.4-ethercat.c \
	e1000_ethtool-3.4-orig.c \
	e1000_ethtool-3.6-ethercat.c \
	e1000_ethtool-3.6-orig.c \
	e1000_ethtool-3.8-ethercat.c \
	e1000_ethtool-3.8-orig.c \
	e1000_ethtool-4.4-ethercat.c \
	e1000_ethtool-4.4-orig.c \
	e1000_ethtool-5.10-ethercat.c \
	e1000_ethtool-5.10-orig.c \
	e1000_ethtool-5.14-ethercat.c \
	e1000_ethtool-5.14-orig.c \
	e1000_ethtool-5.15-ethercat.c \
	e1000_ethtool-5.15-orig.c \
	e1000_ethtool-6.1-ethercat.c \
	e1000_ethtool-6.1-orig.c \
	e1000_ethtool-6.12-ethercat.c \
	e1000_ethtool-6.12-orig.c \
	e1000_ethtool-6.4-ethercat.c \
	e1000_ethtool-6.4-orig.c \
	e1000_hw-3.0-ethercat.c \
	e1000_hw-3.0-ethercat.h \
	e1000_hw-3.0-orig.c \
	e1000_hw-3.0-orig.h \
	e1000_hw-3.10-ethercat.c \
	e1000_hw-3.10-ethercat.h \
	e1000_hw-3.10-orig.c \
	e1000_hw-3.10-orig.h \
	e1000_hw-3.12-ethercat.c \
	e1000_hw-3.12-ethercat.h \
	e1000_hw-3.12-orig.c \
	e1000_hw-3.12-orig.h \
	e1000_hw-3.14-ethercat.c \
	e1000_hw-3.14-ethercat.h \
	e1000_hw-3.14-orig.c \
	e1000_hw-3.14-orig.h \
	e1000_hw-3.16-ethercat.c \
	e1000_hw-3.16-ethercat.h \
	e1000_hw-3.16-orig.c \
	e1000_hw-3.16-orig.h \
	e1000_hw-3.4-ethercat.c \
	e1000_hw-3.4-ethercat.h \
	e1000_hw-3.4-orig.c \
	e1000_hw-3.4-orig.h \
	e1000_hw-3.6-ethercat.c \
	e1000_hw-3.6-ethercat.h \
	e1000_hw-3.6-orig.c \
	e1000_hw-3.6-orig.h \
	e1000_hw-3.8-ethercat.c \
	e1000_hw-3.8-ethercat.h \
	e1000_hw-3.8-orig.c \
	e1000_hw-3.8-orig.h \
	e1000_hw-4.4-ethercat.c \
	e1000_hw-4.4-ethercat.h \
	e1000_hw-4.4-orig.c \
	e1000_hw-4.4-orig.h \
	e1000_hw-5.10-ethercat.c \
	e1000_hw-5.10-ethercat.h \
	e1000_hw-5.10-orig.c \
	e1000_hw-5.10-orig.h \
	e1000_hw-5.14-ethercat.c \
	e1000_hw-5.14-ethercat.h \
	e1000_hw-5.14-orig.c \
	e1000_hw-5.14-orig.h \
	e1000_hw-5.15-ethercat.c \
	e1000_hw-5.15-ethercat.h \
	e1000_hw-5.15-orig.c \
	e1000_hw-5.15-orig.h \
	e1000_hw-6.1-ethercat.c \
	e1000_hw-6.1-ethercat.h \
	e1000_hw-6.1-orig.c \
	e1000_hw-6.1-orig.h \
	e1000_hw-6.12-ethercat.c \
	e1000_hw-6.12-ethercat.h \
	e1000_hw-6.12-orig.c \
	e1000_hw-6.12-orig.h \
	e1000_hw-6.4-ethercat.c \
	e1000_hw-6.4-ethercat.h \
	e1000_hw-6.4-orig.c \
	e1000_hw-6.4-orig.h \
	e1000_main-3.0-ethercat.c \
	e1000_main-3.0-orig.c \
	e1000_main-3.10-ethercat.c \
	e1000_main-3.10-orig.c \
	e1000_main-3.12-ethercat.c \
	e1000_main-3.12-orig.c \
	e1000_main-3.14-ethercat.c \
	e1000_main-3.14-orig.c \
	e1000_main-3.16-ethercat.c \
	e1000_main-3.16-orig.c \
	e1000_main-3.4-ethercat.c \
	e1000_main-3.4-orig.c \
	e1000_main-3.6-ethercat.c \
	e1000_main-3.6-orig.c \
	e1000_main-3.8-ethercat.c \
	e1000_main-3.8-orig.c \
	e1000_main-4.4-ethercat.c \
	e1000_main-4.4-orig.c \
	e1000_main-5.10-ethercat.c \
	e1000_main-5.10-orig.c \
	e1000_main-5.14-ethercat.c \
	e1000_main-5.14-orig.c \
	e1000_main-5.15-ethercat.c \
	e1000_main-5.15-orig.c \
	e1000_main-6.1-ethercat.c \
	e1000_main-6.1-orig.c \
	e1000_main-6.12-ethercat.c \
	e1000_main-6.12-orig.c \
	e1000_main-6.4-ethercat.c \
	e1000_main-6.4-orig.c \
	e1000_osdep-3.0-ethercat.h \
	e1000_osdep-3.0-orig.h \
	e1000_osdep-3.10-ethercat.h \
	e1000_osdep-3.10-orig.h \
	e1000_osdep-3.12-ethercat.h \
	e1000_osdep-3.12-orig.h \
	e1000_osdep-3.14-ethercat.h \
	e1000_osdep-3.14-orig.h \
	e1000_osdep-3.16-ethercat.h \
	e1000_osdep-3.16-orig.h \
	e1000_osdep-3.4-ethercat.h \
	e1000_osdep-3.4-orig.h \
	e1000_osdep-3.6-ethercat.h \
	e1000_osdep-3.6-orig.h \
	e1000_osdep-3.8-ethercat.h \
	e1000_osdep-3.8-orig.h \
	e1000_osdep-4.4-ethercat.h \
	e1000_osdep-4.4-orig.h \
	e1000_osdep-5.10-ethercat.h \
	e1000_osdep-5.10-orig.h \
	e1000_osdep-5.14-ethercat.h \
	e1000_osdep-5.14-orig.h \
	e1000_osdep-5.15-ethercat.h \
	e1000_osdep-5.15-orig.h \
	e1000_osdep-6.1-ethercat.h \
	e1000_osdep-6.1-orig.h \
	e1000_osdep-6.12-ethercat.h \
	e1000_osdep-6.12-orig.h \
	e1000_osdep-6.4-ethercat.h \
	e1000_osdep-6.4-orig.h \
	e1000_param-3.0-ethercat.c \
	e1000_param-3.0-orig.c \
	e1000_param-3.10-ethercat.c \
	e1000_param-3.10-orig.c \
	e1000_param-3.12-ethercat.c \
	e1000_param-3.12-orig.c \
	e1000_param-3.14-ethercat.c \
	e1000_param-3.14-orig.c \
	e1000_param-3.16-ethercat.c \
	e1000_param-3.16-orig.c \
	e1000_param-3.4-ethercat.c \
	e1000_param-3.4-orig.c \
	e1000_param-3.6-ethercat.c \
	e1000_param-3.6-orig.c \
	e1000_param-3.8-ethercat.c \
	e1000_param-3.8-orig.c \
	e1000_param-4.4-ethercat.c \
	e1000_param-4.4-orig.c \
	e1000_param-5.10-ethercat.c \
	e1000_param-5.10-orig.c \
	e1000_param-5.14-ethercat.c \
	e1000_param-5.14-orig.c \
	e1000_param-5.15-ethercat.c \
	e1000_param-5.15-orig.c \
	e1000_param-6.1-ethercat.c \
	e1000_param-6.1-orig.c \
	e1000_param-6.12-ethercat.c \
	e1000_param-6.12-orig.c \
	e1000_param-6.4-ethercat.c \
	e1000_param-6.4-orig.c \
	update.sh

#-----------------------------------------------------------------------------
