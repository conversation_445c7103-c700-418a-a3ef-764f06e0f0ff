#-----------------------------------------------------------------------------
#
#  Copyright (C) 2006-2017  <PERSON><PERSON><PERSON>, Ingenieurgemeinschaft IgH
#
#  This file is part of the IgH EtherCAT Master.
#
#  The IgH EtherCAT Master is free software; you can redistribute it and/or
#  modify it under the terms of the GNU General Public License version 2, as
#  published by the Free Software Foundation.
#
#  The IgH EtherCAT Master is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
#  Public License for more details.
#
#  You should have received a copy of the GNU General Public License along
#  with the IgH EtherCAT Master; if not, write to the Free Software
#  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
#
#  ---
#
#  vim: syntax=make
#
#-----------------------------------------------------------------------------

src := @abs_srcdir@
ccflags-y := -I@abs_top_builddir@

TOPDIR := $(src)/../..

REV := $(shell if test -s $(TOPDIR)/revision; then \
		cat $(TOPDIR)/revision; \
	else \
		git -C $(TOPDIR) describe 2>/dev/null || echo "unknown"; \
	fi)

ifeq (@ENABLE_IGC@,1)
	obj-m += ec_igc.o

	ec_igc-objs := \
		igc_xdp-@KERNEL_IGC@-ethercat.o \
		igc_tsn-@KERNEL_IGC@-ethercat.o \
		igc_base-@KERNEL_IGC@-ethercat.o \
		igc_dump-@KERNEL_IGC@-ethercat.o \
		igc_main-@KERNEL_IGC@-ethercat.o \
		igc_ptp-@KERNEL_IGC@-ethercat.o \
		igc_diag-@KERNEL_IGC@-ethercat.o \
		igc_i225-@KERNEL_IGC@-ethercat.o \
		igc_mac-@KERNEL_IGC@-ethercat.o \
		igc_phy-@KERNEL_IGC@-ethercat.o \
		igc_ethtool-@KERNEL_IGC@-ethercat.o \
		igc_nvm-@KERNEL_IGC@-ethercat.o

	ifeq (@HAS_IGC_LEDS@,1)
		ec_igc-objs += igc_leds-@KERNEL_IGC@-ethercat.o
	endif


ifeq (@ENABLE_DRIVER_RESOURCE_VERIFYING@,1)
	ccflags-y := -DEC_ENABLE_DRIVER_RESOURCE_VERIFYING
endif

	CFLAGS_igc_main-@KERNEL_IGC@-ethercat.o = -DREV=$(REV)
endif

KBUILD_EXTRA_SYMBOLS := \
	@abs_top_builddir@/$(LINUX_SYMVERS) \
	@abs_top_builddir@/master/$(LINUX_SYMVERS)

#-----------------------------------------------------------------------------
