LIBETHERCAT_1.5.2 {
	global:
		ecrt_domain_data;
		ecrt_domain_process;
		ecrt_domain_queue;
		ecrt_domain_reg_pdo_entry_list;
		ecrt_domain_size;
		ecrt_domain_state;
		ecrt_master;
		ecrt_master_activate;
		ecrt_master_application_time;
		ecrt_master_create_domain;
		ecrt_master_deactivate;
		ecrt_master_get_pdo;
		ecrt_master_get_pdo_entry;
		ecrt_master_get_slave;
		ecrt_master_get_sync_manager;
		ecrt_master_link_state;
		ecrt_master_read_idn;
		ecrt_master_receive;
		ecrt_master_reference_clock_time;
		ecrt_master_reserve;
		ecrt_master_reset;
		ecrt_master_sdo_download;
		ecrt_master_sdo_download_complete;
		ecrt_master_sdo_upload;
		ecrt_master_select_reference_clock;
		ecrt_master_send;
		ecrt_master_set_send_interval;
		ecrt_master_slave_config;
		ecrt_master_state;
		ecrt_master_sync_monitor_process;
		ecrt_master_sync_monitor_queue;
		ecrt_master_sync_reference_clock;
		ecrt_master_sync_slave_clocks;
		ecrt_master_write_idn;
		ecrt_open_master;
		ecrt_reg_request_data;
		ecrt_reg_request_read;
		ecrt_reg_request_state;
		ecrt_reg_request_write;
		ecrt_release_master;
		ecrt_request_master;
		ecrt_sdo_request_data;
		ecrt_sdo_request_data_size;
		ecrt_sdo_request_index;
		ecrt_sdo_request_read;
		ecrt_sdo_request_state;
		ecrt_sdo_request_timeout;
		ecrt_sdo_request_write;
		ecrt_slave_config_complete_sdo;
		ecrt_slave_config_create_reg_request;
		ecrt_slave_config_create_sdo_request;
		ecrt_slave_config_create_soe_request;
		ecrt_slave_config_create_voe_handler;
		ecrt_slave_config_dc;
		ecrt_slave_config_emerg_clear;
		ecrt_slave_config_emerg_overruns;
		ecrt_slave_config_emerg_pop;
		ecrt_slave_config_emerg_size;
		ecrt_slave_config_idn;
		ecrt_slave_config_pdo_assign_add;
		ecrt_slave_config_pdo_assign_clear;
		ecrt_slave_config_pdo_mapping_add;
		ecrt_slave_config_pdo_mapping_clear;
		ecrt_slave_config_pdos;
		ecrt_slave_config_reg_pdo_entry;
		ecrt_slave_config_reg_pdo_entry_pos;
		ecrt_slave_config_sdo;
		ecrt_slave_config_sdo16;
		ecrt_slave_config_sdo32;
		ecrt_slave_config_sdo8;
		ecrt_slave_config_state;
		ecrt_slave_config_sync_manager;
		ecrt_slave_config_watchdog;
		ecrt_version_magic;
		ecrt_voe_handler_data;
		ecrt_voe_handler_data_size;
		ecrt_voe_handler_execute;
		ecrt_voe_handler_read;
		ecrt_voe_handler_read_nosync;
		ecrt_voe_handler_received_header;
		ecrt_voe_handler_send_header;
		ecrt_voe_handler_write;
	local:
		# Hide all C++ symbols in libfakeethercat
		_Z*;
};

LIBETHERCAT_1.5.3 {
	global:
		ecrt_master_sync_reference_clock_to;
		ecrt_read_lreal;
		ecrt_read_real;
		ecrt_slave_config_flag;
		ecrt_soe_request_data;
		ecrt_soe_request_data_size;
		ecrt_soe_request_idn;
		ecrt_soe_request_read;
		ecrt_soe_request_state;
		ecrt_soe_request_timeout;
		ecrt_soe_request_write;
		ecrt_write_lreal;
		ecrt_write_real;
} LIBETHERCAT_1.5.2;

LIBETHERCAT_1.6 {
	global:
		ecrt_master_scan_progress;
		ecrt_slave_config_eoe_mac_address;
		ecrt_slave_config_eoe_ip_address;
		ecrt_slave_config_eoe_subnet_mask;
		ecrt_slave_config_eoe_default_gateway;
		ecrt_slave_config_eoe_dns_address;
		ecrt_slave_config_eoe_hostname;
		ecrt_slave_config_state_timeout;
} LIBETHERCAT_1.5.3;
