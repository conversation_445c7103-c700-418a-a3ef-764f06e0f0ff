/*****************************************************************************
 *
 *  Copyright (C) 2006-2009  <PERSON><PERSON><PERSON>, Ingenieurgemeinschaft IgH
 *
 *  This file is part of the IgH EtherCAT Master.
 *
 *  The IgH EtherCAT Master is free software; you can redistribute it and/or
 *  modify it under the terms of the GNU General Public License version 2, as
 *  published by the Free Software Foundation.
 *
 *  The IgH EtherCAT Master is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
 *  Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License along
 *  with the IgH EtherCAT Master; if not, write to the Free Software
 *  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
 *
 ****************************************************************************/

#ifndef __COMMANDREGWRITE_H__
#define __COMMANDREGWRITE_H__

#include "Command.h"
#include "DataTypeHandler.h"

/****************************************************************************/

class CommandRegWrite:
    public Command,
    public DataTypeHandler
{
    public:
        CommandRegWrite();

        string helpString(const string &) const;
        void execute(const StringVector &);

    private:
        void loadRegData(ec_ioctl_slave_reg_t *, const istream &);
};

/****************************************************************************/

#endif
