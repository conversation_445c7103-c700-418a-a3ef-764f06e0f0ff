#!/bin/bash

#------------------------------------------------------------------------------
#
#  Start script for EtherCAT to use with systemd
#
#  Copyright (C) 2006-2021  <PERSON><PERSON><PERSON>, Ingenieurgemeinschaft IgH
#
#  This file is part of the IgH EtherCAT Master.
#
#  The IgH EtherCAT Master is free software; you can redistribute it and/or
#  modify it under the terms of the GNU General Public License version 2, as
#  published by the Free Software Foundation.
#
#  The IgH EtherCAT Master is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
#  Public License for more details.
#
#  You should have received a copy of the GNU General Public License along
#  with the IgH EtherCAT Master; if not, write to the Free Software
#  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
#
#  vim: expandtab sw=4 tw=78
#
#------------------------------------------------------------------------------

LSMOD=/sbin/lsmod
MODPROBE=/sbin/modprobe
RMMOD=/sbin/rmmod
MODINFO=/sbin/modinfo
IP=/bin/ip

ETHERCAT=@bindir@/ethercat

#------------------------------------------------------------------------------

if [ "$1" = "-c" ]; then
    ETHERCAT_CONFIG="$2"
    COMMAND="$3"
else
    ETHERCAT_CONFIG=@sysconfdir@/ethercat.conf
    COMMAND="$1"
fi

#------------------------------------------------------------------------------

if [ ! -r ${ETHERCAT_CONFIG} ]; then
    echo ${ETHERCAT_CONFIG} not existing;
    exit 6
fi

# shellcheck source=/etc/ethercat.conf
. ${ETHERCAT_CONFIG}

#------------------------------------------------------------------------------

is_mac_address() {
    local x='[0-9a-fA-F]'
    echo "$1" | grep -qE "^($x$x:){5}$x$x\$" -
}

#------------------------------------------------------------------------------

parse_mac_address() {
    local DEVICENAMETOMAC
    if [ -z "${1}" ] || is_mac_address "${1}"; then
        MAC="${1}"
    else
        DEVICENAMETOMAC=$("${IP}" address show dev "${1}" |
            awk '/link\/ether/ { print $2; }')
        if is_mac_address "${DEVICENAMETOMAC}"; then
            MAC="${DEVICENAMETOMAC}"
        else
            echo Invalid MAC address or interface name \""${1}"\" \
                in ${ETHERCAT_CONFIG}
            exit 1
        fi
    fi
}

#------------------------------------------------------------------------------

case "$COMMAND" in

start)
    # bring up all updown interfaces before anything else
    for interface in $UPDOWN_INTERFACES; do
        $IP link set dev $interface up
    done

    # construct DEVICES and BACKUPS from configuration variables
    DEVICES=""
    BACKUPS=""
    MASTER_INDEX=0

    while true; do
        DEVICE=$(eval echo "\${MASTER${MASTER_INDEX}_DEVICE}")
        BACKUP=$(eval echo "\${MASTER${MASTER_INDEX}_BACKUP}")
        if [ -z "${DEVICE}" ]; then break; fi

        if [ ${MASTER_INDEX} -gt 0 ]; then
            DEVICES=${DEVICES},
            BACKUPS=${BACKUPS},
        fi

        parse_mac_address "${DEVICE}"
        DEVICES=${DEVICES}${MAC}

        parse_mac_address "${BACKUP}"
        BACKUPS=${BACKUPS}${MAC}

        MASTER_INDEX=$((${MASTER_INDEX} + 1))
    done

    if [ -z "${DEVICES}" ]; then
        echo "ERROR: No network cards for EtherCAT specified."
        echo -n "Please edit ${ETHERCAT_CONFIG} with root permissions"
        echo -n " and set MASTER0_DEVICE variable to either a "
        echo "network interface name (like eth0) or to a MAC address."
        exit 1
    fi

    # load master module
    if ! ${MODPROBE} ${MODPROBE_FLAGS} ec_master \
            main_devices="${DEVICES}" backup_devices="${BACKUPS}"; then
        exit 1
    fi

    LOADED_MODULES=ec_master

    # check for modules to replace
    for MODULE in ${DEVICE_MODULES}; do
        ECMODULE=ec_${MODULE}
        if ! ${MODINFO} "${ECMODULE}" > /dev/null; then
            continue # ec_* module not found
        fi

        if [ "${MODULE}" != "generic" ] && [ "${MODULE}" != "ccat" ]; then
            # unload standard module and check if unloading was successful
            ${RMMOD} "${MODULE}" 2> /dev/null || true
            if ${LSMOD} | grep "^${MODULE} " > /dev/null; then
                # could not unload module
                ${RMMOD} ${LOADED_MODULES}
                exit 1
            fi
        fi

        if ! ${MODPROBE} ${MODPROBE_FLAGS} "${ECMODULE}"; then
            if [ "${MODULE}" != "generic" ] && [ "${MODULE}" != "ccat" ]; then
                ${MODPROBE} ${MODPROBE_FLAGS} "${MODULE}" # try to restore
            fi
            ${RMMOD} ${LOADED_MODULES}
            exit 1
        fi

        LOADED_MODULES="${ECMODULE} ${LOADED_MODULES}"
    done

    exit 0
    ;;

#------------------------------------------------------------------------------

stop)
    # unload EtherCAT device modules
    for MODULE in ${DEVICE_MODULES} master; do
        ECMODULE=ec_${MODULE}
        if ! ${LSMOD} | grep -q "^${ECMODULE} "; then
            continue # ec_* module not loaded
        fi
        if ! ${RMMOD} "${ECMODULE}"; then
            exit 1
        fi;
    done

    sleep 1

    # load standard modules again
    for MODULE in ${DEVICE_MODULES}; do
        if [ "${MODULE}" == "generic" ] || [ "${MODULE}" == "ccat" ]; then
            continue
        fi
        ${MODPROBE} ${MODPROBE_FLAGS} "${MODULE}"
    done

    # bring down all updown interfaces
    for interface in $UPDOWN_INTERFACES; do
        $IP link set dev $interface down
    done

    exit 0
    ;;

#------------------------------------------------------------------------------

restart)
    $0 stop || exit 1
    sleep 1
    $0 start
    ;;

#------------------------------------------------------------------------------

status)
    echo "Checking for EtherCAT master @VERSION@ "

    # count masters in configuration file
    MASTER_COUNT=0
    while true; do
        DEVICE=$(eval echo "\${MASTER${MASTER_COUNT}_DEVICE}")
        if [ -z "${DEVICE}" ]; then break; fi
        MASTER_COUNT=$((${MASTER_COUNT} + 1))
    done

    RESULT=0

    for i in $(seq 0 "$((${MASTER_COUNT} - 1))"); do
        echo -n "Master${i} "

        # Check if the master is in idle or operation phase
        ${ETHERCAT} master --master "${i}" 2>/dev/null | \
            grep -qE 'Phase:[[:space:]]*Idle|Phase:[[:space:]]*Operation'
        EXITCODE=$?

        if [ ${EXITCODE} -eq 0 ]; then
            echo " running"
        else
            echo " dead"
            RESULT=1
        fi
    done

    exit ${RESULT}
    ;;

#------------------------------------------------------------------------------

*)
    echo "USAGE: $0 [-c path/to/ethercat.conf] {start|stop|restart|status}"
    exit 1
    ;;
esac

#------------------------------------------------------------------------------
