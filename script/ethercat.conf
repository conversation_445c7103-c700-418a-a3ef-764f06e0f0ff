#------------------------------------------------------------------------------
#
#  EtherCAT master configuration file for use with ethercatctl.
#
#  vim: spelllang=en spell tw=78
#
#------------------------------------------------------------------------------

#
# Main Ethernet devices.
#
# The MASTER<X>_DEVICE variable specifies the Ethernet device for a master
# with index 'X'.
#
# Specify the MAC address (hexadecimal with colons) of the Ethernet device to
# use. Example: "00:00:08:44:ab:66"
#
# Alternatively, a network interface name can be specified. The interface
# name will be resolved to a MAC address using the 'ip' command.
# Example: "eth0"
#
# The broadcast address "ff:ff:ff:ff:ff:ff" has a special meaning: It tells
# the master to accept the first device offered by any Ethernet driver.
#
# The MASTER<X>_DEVICE variables also determine, how many masters will be
# created: A non-empty variable MASTER0_DEVICE will create one master, adding a
# non-empty variable MASTER1_DEVICE will create a second master, and so on.
#
# Examples:
# MASTER0_DEVICE="00:00:08:44:ab:66"
# MASTER0_DEVICE="eth0"
#
MASTER0_DEVICE=""
#MASTER1_DEVICE=""

#
# Backup Ethernet devices
#
# The MASTER<X>_BACKUP variables specify the devices used for redundancy. They
# behaves nearly the same as the MASTER<X>_DEVICE variable, except that it
# does not interpret the ff:ff:ff:ff:ff:ff address.
#
#MASTER0_BACKUP=""

#
# Ethernet driver modules to use for EtherCAT operation.
#
# Specify a non-empty list of Ethernet drivers, that shall be used for
# EtherCAT operation.
#
# Except for the generic Ethernet driver module, the init script will try to
# unload the usual Ethernet driver modules in the list and replace them with
# the EtherCAT-capable ones. If a certain (EtherCAT-capable) driver is not
# found, a warning will appear.
#
# Possible values: 8139too, e100, e1000, e1000e, r8169, generic, ccat, igb, igc, genet, dwmac-intel, stmmac-pci.
# Separate multiple drivers with spaces.
# A list of all matching kernel versions can be found here:
# https://docs.etherlab.org/ethercat/1.6/doxygen/devicedrivers.html
#
# Note: The e100, e1000, e1000e, r8169, ccat, igb and igc drivers are not built by
# default. Enable them with the --enable-<driver> configure switches.
#
DEVICE_MODULES=""

# If you have any issues about network interfaces not being configured
# properly, systemd may need some additional infos about your setup.
# Have a look at the service file, you'll find some details there.
#

#
# List of interfaces to bring up and down automatically.
#
# Specify a space-separated list of interface names (such as eth0 or
# enp0s1) that shall be brought up on `ethercatctl start` and down on
# `ethercatctl stop`.
#
# When using the generic driver, the corresponding Ethernet device has to be
# activated before the master is started, otherwise all frames will time out.
# This the perfect use-case for `UPDOWN_INTERFACES`.
#
UPDOWN_INTERFACES=""

#
# Flags for loading kernel modules.
#
# This can usually be left empty. Adjust this variable, if you have problems
# with module loading.
#
#MODPROBE_FLAGS="-b"

#------------------------------------------------------------------------------
