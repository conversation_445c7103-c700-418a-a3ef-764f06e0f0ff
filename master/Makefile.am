#-----------------------------------------------------------------------------
#
#  Copyright (C) 2006-2012  <PERSON><PERSON><PERSON>, Ingenieurgemeinschaft IgH
#
#  This file is part of the IgH EtherCAT Master.
#
#  The IgH EtherCAT Master is free software; you can redistribute it and/or
#  modify it under the terms of the GNU General Public License version 2, as
#  published by the Free Software Foundation.
#
#  The IgH EtherCAT Master is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
#  Public License for more details.
#
#  You should have received a copy of the GNU General Public License along
#  with the IgH EtherCAT Master; if not, write to the Free Software
#  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
#
#-----------------------------------------------------------------------------

include $(top_srcdir)/Makefile.kbuild

# using HEADERS to enable tags target
noinst_HEADERS = \
	cdev.c cdev.h \
	coe_emerg_ring.c coe_emerg_ring.h \
	datagram.c datagram.h \
	datagram_pair.c datagram_pair.h \
	debug.c debug.h \
	device.c device.h \
	domain.c domain.h \
	doxygen.c \
	eoe_request.c eoe_request.h \
	ethernet.c ethernet.h \
	flag.c flag.h \
	fmmu_config.c fmmu_config.h \
	foe.h \
	foe_request.c foe_request.h \
	fsm_change.c fsm_change.h \
	fsm_coe.c fsm_coe.h \
	fsm_eoe.c fsm_eoe.h \
	fsm_foe.c fsm_foe.h \
	fsm_master.c fsm_master.h \
	fsm_pdo.c fsm_pdo.h \
	fsm_pdo_entry.c fsm_pdo_entry.h \
	fsm_sii.c fsm_sii.h \
	fsm_slave.c fsm_slave.h \
	fsm_slave_config.c fsm_slave_config.h \
	fsm_slave_scan.c fsm_slave_scan.h \
	fsm_soe.c fsm_soe.h \
	globals.h \
	ioctl.c ioctl.h \
	mailbox.c mailbox.h \
	master.c master.h \
	module.c \
	pdo.c pdo.h \
	pdo_entry.c pdo_entry.h \
	pdo_list.c pdo_list.h \
	reg_request.c reg_request.h \
	rtdm-ioctl.c \
	rtdm.c rtdm.h \
	rtdm_details.h \
	rtdm_xenomai_v3.c \
	sdo.c sdo.h \
	sdo_entry.c sdo_entry.h \
	sdo_request.c sdo_request.h \
	slave.c slave.h \
	slave_config.c slave_config.h \
	soe_errors.c \
	soe_request.c soe_request.h \
	sync.c sync.h \
	sync_config.c sync_config.h \
	voe_handler.c voe_handler.h

#-----------------------------------------------------------------------------
