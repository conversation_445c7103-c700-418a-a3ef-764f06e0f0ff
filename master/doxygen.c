/*****************************************************************************
 *
 *  Copyright (C) 2006-2023  Florian Pose, Ingenieurgemeinschaft IgH
 *
 *  This file is part of the IgH EtherCAT Master.
 *
 *  The IgH EtherCAT Master is free software; you can redistribute it and/or
 *  modify it under the terms of the GNU General Public License version 2, as
 *  published by the Free Software Foundation.
 *
 *  The IgH EtherCAT Master is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
 *  Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License along
 *  with the IgH EtherCAT Master; if not, write to the Free Software
 *  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
 *
 ****************************************************************************/

// This file only contains the doxygen mainpage.

/** \file
 * Doxygen mainpage only.
 */

/****************************************************************************/

/**
   \mainpage

   \section sec_general General information

   This HTML contains the complete code documentation.

   The API documentations are in the <a href="modules.html">modules
   section</a>.

   A list of all native network card drivers can be found
   <a href="devicedrivers.html">here</a>.

   A <a href="libfakeethercat.html">second userspace library</a> can be used for a dry-run mode
   or simulating Process Data.

   For information how to build and install, see the INSTALL file in the source
   root.

   \section sec_contact Contact

   \verbatim
   Florian Pose <<EMAIL>>
   Ingenieurgemeinschaft IgH
   Nordsternstraße 66
   D-45329 Essen
   http://igh.de
   \endverbatim

   \section sec_license License

   \verbatim
   Copyright (C) 2006-2023  Florian Pose, Ingenieurgemeinschaft IgH

   This file is part of the IgH EtherCAT Master.

   The IgH EtherCAT Master is free software; you can redistribute it and/or
   modify it under the terms of the GNU General Public License version 2, as
   published by the Free Software Foundation.

   The IgH EtherCAT Master is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
   Public License for more details.

   You should have received a copy of the GNU General Public License along
   with the IgH EtherCAT Master; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA

   \endverbatim
*/

/****************************************************************************/
